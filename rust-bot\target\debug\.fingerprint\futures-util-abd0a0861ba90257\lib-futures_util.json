{"rustc": 11410426090777951712, "features": "[\"alloc\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17467636112133979524, "path": 7566926197534102390, "deps": [[5103565458935487, "futures_io", false, 4327888110651733805], [1615478164327904835, "pin_utils", false, 14185876727333389912], [1811549171721445101, "futures_channel", false, 4581757529666331918], [1906322745568073236, "pin_project_lite", false, 11354155332385169719], [5451793922601807560, "slab", false, 13644490449199547910], [7013762810557009322, "futures_sink", false, 5916858244798959902], [7620660491849607393, "futures_core", false, 10848690019005405091], [15932120279885307830, "memchr", false, 15292374738451916218], [16240732885093539806, "futures_task", false, 17466230511372174808]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-abd0a0861ba90257/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}