{"rustc": 11410426090777951712, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 17255432589167795725, "path": 1143878777299803267, "deps": [[1213098572879462490, "json5_rs", false, 1302321928203849378], [1965680986145237447, "yaml_rust2", false, 15141175185277775515], [2244620803250265856, "ron", false, 8885026468427225104], [6502365400774175331, "nom", false, 13569576578978316866], [6517602928339163454, "path<PERSON><PERSON>", false, 3920212701463783292], [9689903380558560274, "serde", false, 3688603362250867157], [11946729385090170470, "async_trait", false, 3048550345113818952], [13475460906694513802, "convert_case", false, 3817527236901462684], [14618892375165583068, "ini", false, 7552765154906353395], [15367738274754116744, "serde_json", false, 14291916180379449153], [15609422047640926750, "toml", false, 7577483486957051114]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/config-be89c18581394db3/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}