{"rustc": 11410426090777951712, "features": "[\"std\"]", "declared_features": "[\"default\", \"export-internal\", \"grammar-extras\", \"not-bootstrap-in-src\", \"std\"]", "target": 3031267579843285925, "profile": 2225463790103693989, "path": 865189019651271772, "deps": [[3060637413840920116, "proc_macro2", false, 13259542598500423923], [3221585212778410572, "pest", false, 11687170170820623565], [3395339557636834855, "pest_meta", false, 1700586044321149787], [4974441333307933176, "syn", false, 13108380705740598857], [17990358020177143287, "quote", false, 4967107860177656466]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pest_generator-64a35fa5848b5fed/dep-lib-pest_generator", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}