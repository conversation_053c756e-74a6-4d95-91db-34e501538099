{"rustc": 11410426090777951712, "features": "[]", "declared_features": "[\"backtrace\", \"deadlock_detection\", \"nightly\", \"petgraph\", \"thread-id\"]", "target": 12558056885032795287, "profile": 2241668132362809309, "path": 17343000983642831119, "deps": [[2828590642173593838, "cfg_if", false, 579446646406430353], [3666196340704888985, "smallvec", false, 8147833125738349794], [4269498962362888130, "build_script_build", false, 13721487733301622778], [4684437522915235464, "libc", false, 8466636694638180743]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/parking_lot_core-0c31aada1f014a1c/dep-lib-parking_lot_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}