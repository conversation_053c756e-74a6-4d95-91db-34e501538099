{"general": {"prefix": "!", "adminRole": "1337103887697707109", "modRole": "1337104111967010877"}, "entry": {"welcomeChannel": "1378003678484627486", "rulesChannel": "ID_SALON_LOG", "entryRequestChannelId": "1378004681724268676", "verificationRole": ""}, "modmail": {"modmailCategory": "1371178119792427179", "modmailLogs": "1372227326150185040"}, "tickets": {"ticketCategory": "1378003477418217472", "acceptedEntryCategoryId": "1350478995912658944", "supportRole": "1371178637570609193", "ticketLogs": "1372227326150185040"}, "logging": {"modLogs": "", "messageLogs": "1377426635556388966", "voiceLogs": "1378034290767560775", "memberLogs": "", "roleLogChannelId": "", "excludedChannels": [], "excludedRoles": [], "excludedUsers": [], "roleLogsExcludedRoles": [], "moderationWebhookUrl": "", "messagesWebhookUrl": "https://discord.com/api/webhooks/1382141594295210054/GtTIXpd32B7D2MuGJnL2Dn4eQGDq6zW66rLjwNhRc4YhA9yogk63eptmVHMaEKW8OTKJ", "messagesEditedWebhookUrl": "", "messagesDeletedWebhookUrl": "", "voiceWebhookUrl": "https://discord.com/api/webhooks/1382388554600153179/ehNIvXh9fAlnKGvddud3rcQOU2L82SYRhA3z1SizbdSj5_yo4i-LrsXHsA05mBLaupYt", "rolesWebhookUrl": "", "memberWebhookUrl": "", "ticketsWebhookUrl": ""}, "welcome": {"welcomeMessage": "Bienvenue @user sur notre serveur !", "rulesMessage": "", "welcomeDM": ""}, "entryModal": {"title": "Formulaire de demande d'accès", "fields": [{"customId": "pseudo_input", "label": "Quel est votre pseudo principal ?", "style": "Short", "required": true, "placeholder": "Ex: SuperJoueur123"}, {"customId": "motivation_input", "label": "Quelles sont vos motivations à rejoindre ?", "style": "Paragraph", "required": true, "placeholder": "Décrivez en quelques mots pourquoi vous souhaitez nous rejoindre."}, {"customId": "experience_input", "label": "Expérience similaire (serveurs, jeux) ?", "style": "Paragraph", "required": false, "placeholder": "Si oui, laquelle ?"}, {"customId": "rules_input", "label": "<PERSON><PERSON>-vous lu et compris le règlement ?", "style": "Short", "required": true, "placeholder": "Oui/Non"}, {"customId": "anything_else_input", "label": "<PERSON><PERSON><PERSON>vous quelque chose à ajouter ?", "style": "Paragraph", "required": false, "placeholder": "Remarques, questions, etc."}]}, "confession": {"confessionChannel": "1377427164147879999"}, "games": {"gameChannel": "1377436829145497733", "gameLeaderboard": "", "forbiddenRoleIds": ["1379261130995925032"]}, "kink": {"nsfwChannel": "", "kinkLevels": false, "kinkLogs": ""}, "economy": {"enabled": true, "voiceActivity": {"enabled": true, "pointsPerMinute": 1, "requireUnmuted": true, "requireInChannel": true, "maxPointsPerHour": 60}, "messageActivity": {"enabled": true, "pointsPerReward": 10, "messagesRequired": 10, "minimumWordCount": 3, "cooldownMinutes": 5}, "dailyQuiz": {"enabled": true, "pointsPerCorrectAnswer": 100, "maxPointsPerDay": 500, "hour": 13, "minute": 0}, "games": {"enabled": true, "baseRewards": {"quiz": {"excellent": 30, "good": 20, "passing": 10}, "memory": {"basePoints": [8, 15, 25], "speedBonuses": [3, 6, 10, 15]}, "wordMystery": {"basePoints": 15, "bonusPerRemainingHint": 3}, "anagram": {"basePoints": 12, "bonusPerSpeed": 2}, "guessNumber": {"basePoints": 8, "bonusPerAttempt": 2}, "blackjack": {"winPoints": 15, "drawPoints": 5}, "coinFlip": {"winPoints": 5}}, "difficultyMultipliers": {"easy": 0.7, "normal": 1, "difficult": 1.3, "expert": 1.8}, "bettingSystem": {"enabled": true, "minimumBet": 5, "maximumBet": 100, "winMultiplier": 2}}, "quests": {"enabled": true, "dailyRewards": [25, 40], "weeklyRewards": [150], "maxQuestsPerDay": 3}, "limits": {"maxPointsPerDay": 500, "maxPointsPerHour": 100, "dailyResetHour": 0}}, "levels": {"enabled": false, "levelUpChannel": "", "xpGain": {"message": {"min": 15, "max": 25}, "voice": {"perMinute": 10}}, "cooldowns": {"message": 60000, "voice": 60000}, "multipliers": {"globalMultiplier": 1, "premiumMultiplier": 1.2}, "boosterRoles": {}, "excludedRoles": [], "excludedChannels": [], "rewards": {"coins": {"5": 100, "10": 250, "15": 500, "20": 750, "25": 1000, "30": 1500, "40": 2000, "50": 3000}, "milestones": {"10": {"coins": 500, "message": "🎉 Félicitations ! Vous avez atteint le niveau 10 !"}, "25": {"coins": 1250, "message": "🏆 Impressionnant ! Niveau 25 atteint !"}, "50": {"coins": 3000, "message": "👑 Légendaire ! Vous êtes niveau 50 !"}}}, "messages": {"enabled": true, "templates": {"10": "🎉 **{user}** a atteint le niveau **{level}** ! 🎯\n\n💎 Récompense spéciale débloquée !", "25": "🏆 **{user}** est maintenant niveau **{level}** ! 🔥\n\n⭐ Membre expérimenté !", "50": "👑 **{user}** a atteint le niveau **{level}** ! 🌟\n\n🎖️ Statut légendaire !", "default": "🎉 **{user}** vient d'atteindre le niveau **{level}** !\n\n📊 **Progression:** {xp}/{nextXp} XP ({percentage}%)\n🏆 **XP Total:** {totalXp}"}}}}