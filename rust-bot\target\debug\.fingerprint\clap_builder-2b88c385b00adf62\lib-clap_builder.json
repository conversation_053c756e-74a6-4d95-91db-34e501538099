{"rustc": 11410426090777951712, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 15599109589607159429, "path": 4048903780935610601, "deps": [[5820056977320921005, "anstream", false, 5401397629629661797], [9394696648929125047, "anstyle", false, 5249128871413672236], [11166530783118767604, "strsim", false, 7442586862272412881], [11649982696571033535, "clap_lex", false, 10296921097710695572]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap_builder-2b88c385b00adf62/dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}