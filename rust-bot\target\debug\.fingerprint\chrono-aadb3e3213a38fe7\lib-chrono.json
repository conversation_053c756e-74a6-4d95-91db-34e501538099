{"rustc": 11410426090777951712, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 15315924755136109342, "profile": 2241668132362809309, "path": 2993480380261662316, "deps": [[5157631553186200874, "num_traits", false, 14572034091385385412], [7910860254152155345, "iana_time_zone", false, 10469686396200877504], [9689903380558560274, "serde", false, 3688603362250867157]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/chrono-aadb3e3213a38fe7/dep-lib-chrono", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}