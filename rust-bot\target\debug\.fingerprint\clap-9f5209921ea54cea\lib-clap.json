{"rustc": 11410426090777951712, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 15599109589607159429, "path": 1774627691020417393, "deps": [[4925398738524877221, "clap_derive", false, 1739538799685389107], [14814905555676593471, "clap_builder", false, 3090085747692136559]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-9f5209921ea54cea/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}