{"rustc": 11410426090777951712, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 2241668132362809309, "path": 14491190610870281892, "deps": [[966925859616469517, "ahash", false, 11634619991408110166], [9150530836556604396, "allocator_api2", false, 1719342942675961223]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-8aefdec6b97bbecf/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}