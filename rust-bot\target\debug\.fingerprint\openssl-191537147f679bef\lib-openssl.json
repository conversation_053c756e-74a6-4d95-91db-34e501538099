{"rustc": 11410426090777951712, "features": "[\"default\"]", "declared_features": "[\"aws-lc\", \"bindgen\", \"default\", \"unstable_boringssl\", \"v101\", \"v102\", \"v110\", \"v111\", \"vendored\"]", "target": 17474193825155910204, "profile": 2241668132362809309, "path": 12944628605436361266, "deps": [[2828590642173593838, "cfg_if", false, 579446646406430353], [3722963349756955755, "once_cell", false, 15677475571077396325], [4684437522915235464, "libc", false, 8466636694638180743], [6635237767502169825, "foreign_types", false, 17167161447840720800], [7896293946984509699, "bitflags", false, 2915732471873306218], [8607891082156236373, "build_script_build", false, 15826244632681184146], [9070360545695802481, "ffi", false, 7751757760403988383], [10099563100786658307, "openssl_macros", false, 244717990647901669]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/openssl-191537147f679bef/dep-lib-openssl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}